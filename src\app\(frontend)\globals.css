@import url('https://fonts.googleapis.com/css2?family=Myriad+Pro:wght@300;400;600;700&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  h1,
  h2,
  h3,
  h4,
  h5,
  h6 {
    font-size: unset;
    font-weight: unset;
  }

  :root {
    /* NPI New Color Palette - Homepage Components Only */
    --npi-white: 0 0% 100%;          /* Pure white #FFFFFF */
    --npi-cream: 45 60% 85%;         /* Cream #EFE3BA */
    --npi-brown: 25 30% 42%;         /* Brown #725242 */
    --npi-blue: 195 55% 35%;         /* Blue #25718A */
    --npi-reddish-brown: 15 55% 35%; /* Reddish brown #8A3E25 */
    --npi-black: 0 0% 0%;            /* Black text */

    /* Legacy colors maintained for compatibility */
    --npi-cream-light: 45 60% 85%;   /* #EFE3BA - Cream */
    --npi-cream-medium: 45 60% 85%;  /* #EFE3BA - Cream */
    --npi-sage: 25 30% 42%;          /* #725242 - <PERSON> */
    --npi-brown-dark: 15 55% 35%;    /* #8A3E25 - Reddish brown */
    --npi-charcoal-warm: 0 0% 0%;    /* #000000 - Black */
    --npi-charcoal-deep: 0 0% 0%;    /* #000000 - Black */
    --npi-rust-dark: 15 55% 35%;     /* #8A3E25 - Reddish brown */
    --npi-rust-medium: 15 55% 35%;   /* #8A3E25 - Reddish brown */
    --npi-tan: 25 30% 42%;           /* #725242 - Brown */
    --npi-forest: 195 55% 35%;       /* #25718A - Blue (sparingly) */
    --npi-olive: 195 55% 35%;        /* #25718A - Blue (sparingly) */
    --npi-beige: 45 60% 85%;         /* #EFE3BA - Cream */
    --npi-brown-medium: 25 30% 42%;  /* #725242 - Brown */
    --npi-brown-rich: 15 55% 35%;    /* #8A3E25 - Reddish brown */

    --npi-green: 195 55% 35%;        /* #25718A - Blue (sparingly) */
    --npi-green-light: 195 55% 35%;  /* #25718A - Blue (sparingly) */
    --npi-green-dark: 195 55% 35%;   /* #25718A - Blue (sparingly) */
    --npi-reddish-brown-light: 25 30% 42%; /* #725242 - Brown */
    --npi-reddish-brown-dark: 15 55% 35%;  /* #8A3E25 - Reddish brown */
    --npi-charcoal: 0 0% 0%;         /* #000000 - Black */
    --npi-grey: 25 30% 42%;          /* #725242 - Brown */
    --npi-grey-light: 45 60% 85%;    /* #EFE3BA - Cream */

    /* Legacy burgundy/gold colors for existing components */
    --npi-burgundy: 15 55% 35%;      /* #8A3E25 - Reddish brown */
    --npi-burgundy-light: 25 30% 42%; /* #725242 - Brown */
    --npi-burgundy-dark: 15 55% 35%; /* #8A3E25 - Reddish brown */
    --npi-gold: 45 60% 85%;          /* #EFE3BA - Cream */
    --npi-gold-light: 45 60% 85%;    /* #EFE3BA - Cream */
    --npi-gold-dark: 25 30% 42%;     /* #725242 - Brown */

    /* Base colors using new palette */
    --background: 0 0% 100%;         /* #FFFFFF - White background */
    --foreground: 0 0% 0%;           /* #000000 - Black text */

    --card: 0 0% 100%;               /* #FFFFFF - White cards */
    --card-foreground: 0 0% 0%;      /* #000000 - Black text */

    --popover: 45 60% 85%;           /* #EFE3BA - Cream popover */
    --popover-foreground: 0 0% 0%;   /* #000000 - Black text */

    --primary: 15 55% 35%;           /* #8A3E25 - Reddish brown (main contrast) */
    --primary-foreground: 0 0% 100%; /* #FFFFFF - White text on reddish brown */

    --secondary: 25 30% 42%;         /* #725242 - Brown */
    --secondary-foreground: 0 0% 100%; /* #FFFFFF - White text on brown */

    --tertiary: 45 60% 85%;          /* #EFE3BA - Cream */
    --tertiary-foreground: 0 0% 0%;  /* #000000 - Black text */

    --muted: 25 30% 42%;             /* #725242 - Brown for muted areas */
    --muted-foreground: 0 0% 100%;   /* #FFFFFF - White for muted text */

    --accent: 195 55% 35%;           /* #25718A - Blue accent (sparingly used) */
    --accent-foreground: 0 0% 100%;  /* #FFFFFF - White text on blue */

    --destructive: 0 84% 60%;        /* Standard destructive red */
    --destructive-foreground: 0 0% 98%; /* White text on destructive */

    --border: 25 30% 42%;            /* #725242 - Brown borders */
    --input: 45 60% 85%;             /* #EFE3BA - Cream input backgrounds */
    --ring: 15 55% 35%;              /* #8A3E25 - Reddish brown for focus rings */

    --radius: 0rem;                  /* No border radius for sharp edges */

    --success: 120 50% 35%;          /* Green success */
    --warning: 35 80% 55%;           /* Orange warning */
    --error: 0 70% 50%;              /* Red error */

    /* Extended NPI color palette - Using New Lively Colors */
    --npi-rust-50: 45 25% 95%;       /* Very light cream tint */
    --npi-rust-100: 45 25% 90%;      /* Light cream #E5E1DC */
    --npi-rust-200: 45 20% 82%;      /* Medium cream #CEC9BC */
    --npi-rust-300: 25 30% 52%;      /* Warm tan #A7795E */
    --npi-rust-400: 25 45% 40%;      /* Brown medium #8A6240 */
    --npi-rust-500: 25 60% 26%;      /* Rust medium #6E3C19 */
    --npi-rust-600: 25 50% 20%;      /* Rich brown #4D2D18 */
    --npi-rust-700: 25 30% 28%;      /* Dark brown #46372A */
    --npi-rust-800: 15 50% 13%;      /* Dark rust #34170D */
    --npi-rust-900: 30 8% 8%;        /* Deep charcoal #141311 */

    --npi-cream-50: 45 30% 98%;      /* Very light cream */
    --npi-cream-100: 45 25% 90%;     /* Light cream #E5E1DC */
    --npi-cream-200: 45 20% 82%;     /* Medium cream #CEC9BC */
    --npi-cream-300: 45 25% 75%;     /* Warm beige #CABA9C */
    --npi-cream-400: 60 8% 55%;      /* Sage #8D8F78 */
    --npi-cream-500: 25 30% 52%;     /* Warm tan #A7795E */
    --npi-cream-600: 25 45% 40%;     /* Brown medium #8A6240 */
    --npi-cream-700: 25 30% 28%;     /* Dark brown #46372A */
    --npi-cream-800: 30 8% 18%;      /* Warm charcoal #2F2C29 */
    --npi-cream-900: 30 8% 8%;       /* Deep charcoal #141311 */

    --npi-forest-50: 45 25% 95%;     /* Very light cream tint */
    --npi-forest-100: 45 25% 90%;    /* Light cream #E5E1DC */
    --npi-forest-200: 60 8% 55%;     /* Sage #8D8F78 */
    --npi-forest-300: 80 20% 35%;    /* Olive #4C6444 */
    --npi-forest-400: 150 45% 20%;   /* Medium forest */
    --npi-forest-500: 150 45% 12%;   /* Deep forest #102820 */
    --npi-forest-600: 25 30% 28%;    /* Dark brown #46372A */
    --npi-forest-700: 30 8% 18%;     /* Warm charcoal #2F2C29 */
    --npi-forest-800: 30 8% 8%;      /* Deep charcoal #141311 */
    --npi-forest-900: 15 50% 13%;    /* Dark rust #34170D */

    /* Sage and earth tones */
    --npi-sage-50: 60 15% 95%;       /* Very light sage */
    --npi-sage-100: 60 12% 90%;      /* Light sage */
    --npi-sage-200: 60 10% 80%;      /* Medium light sage */
    --npi-sage-300: 60 8% 70%;       /* Medium sage */
    --npi-sage-400: 60 8% 60%;       /* Medium dark sage */
    --npi-sage-500: 60 8% 55%;       /* Primary sage */
    --npi-sage-600: 60 8% 45%;       /* Dark sage */
    --npi-sage-700: 60 8% 35%;       /* Darker sage */
    --npi-sage-800: 30 8% 25%;       /* Very dark sage */
    --npi-sage-900: 30 8% 8%;        /* Deepest sage (deep charcoal) */

    /* Legacy color scales updated to new palette */
    --npi-burgundy-500: 25 60% 26%;  /* Updated to rust medium */
    --npi-gold-500: 45 25% 75%;      /* Updated to beige */
    --npi-green-500: 150 45% 12%;    /* Updated to forest */
    --npi-reddish-brown-500: 25 60% 26%; /* Updated to rust medium */
  }

  /* Light theme only - dark theme removed for NPI requirements */
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground min-h-[100vh] flex flex-col;
  }
}

html {
  opacity: 0;
}

html[data-theme='dark'],
html[data-theme='light'],
html:not([data-theme]) {
  opacity: initial;
}

/* NPI Custom Styles - Enhanced Light Theme */
@layer components {
  .npi-container {
    @apply max-w-7xl mx-auto px-4 sm:px-6 lg:px-8;
  }

  .npi-section {
    @apply py-4 lg:py-6;
  }

  .npi-section-tight {
    @apply py-2 lg:py-4;
  }

  .npi-section-loose {
    @apply py-8 lg:py-12;
  }

  .npi-spacing-xs {
    @apply space-y-1;
  }

  .npi-spacing-sm {
    @apply space-y-2;
  }

  .npi-spacing-md {
    @apply space-y-3;
  }

  .npi-spacing-lg {
    @apply space-y-4;
  }

  .npi-spacing-xl {
    @apply space-y-6;
  }

  .npi-card {
    @apply bg-card border border-border p-6 shadow-sm hover:shadow-md transition-all duration-200;
  }

  .npi-card-elevated {
    @apply bg-card border border-border p-8 shadow-md hover:shadow-lg transition-all duration-200;
  }

  .npi-button-primary {
    @apply bg-primary text-primary-foreground hover:bg-primary/90 px-6 py-3 font-medium transition-all duration-200 shadow-sm hover:shadow-md;
  }

  .npi-button-secondary {
    @apply bg-secondary text-secondary-foreground hover:bg-secondary/90 px-6 py-3 font-medium transition-all duration-200 shadow-sm hover:shadow-md;
  }

  .npi-button-outline {
    @apply border-2 border-primary text-primary hover:bg-primary hover:text-primary-foreground px-6 py-3 font-medium transition-all duration-200 shadow-sm hover:shadow-md;
  }

  .npi-button-cream {
    @apply bg-tertiary text-primary hover:bg-tertiary/90 px-6 py-3 font-medium transition-all duration-200 shadow-sm hover:shadow-md border border-border;
  }

  .npi-heading-1 {
    @apply text-4xl lg:text-5xl xl:text-6xl font-bold text-foreground leading-[1.1] tracking-[-0.02em];
    font-family: 'Myriad Pro', system-ui, -apple-system, sans-serif;
  }

  .npi-heading-2 {
    @apply text-3xl lg:text-4xl xl:text-5xl font-bold text-foreground leading-[1.2] tracking-[-0.01em];
    font-family: 'Myriad Pro', system-ui, -apple-system, sans-serif;
  }

  .npi-heading-3 {
    @apply text-2xl lg:text-3xl font-semibold text-foreground leading-[1.3] tracking-[-0.005em];
    font-family: 'Myriad Pro', system-ui, -apple-system, sans-serif;
  }

  .npi-heading-4 {
    @apply text-xl lg:text-2xl font-semibold text-foreground leading-[1.4];
    font-family: 'Myriad Pro', system-ui, -apple-system, sans-serif;
  }

  .npi-text-large {
    @apply text-lg lg:text-xl text-muted-foreground leading-[1.6];
    font-family: 'Myriad Pro', system-ui, -apple-system, sans-serif;
  }

  .npi-text-body {
    @apply text-base text-foreground leading-[1.7];
    font-family: 'Myriad Pro', system-ui, -apple-system, sans-serif;
  }

  .npi-text-small {
    @apply text-sm text-muted-foreground leading-[1.5];
    font-family: 'Myriad Pro', system-ui, -apple-system, sans-serif;
  }

  .npi-gradient-bg {
    @apply bg-gradient-to-br from-primary to-primary/80;
  }

  .npi-gradient-light {
    background: linear-gradient(135deg, hsl(var(--tertiary)) 0%, hsl(var(--background)) 100%);
  }

  .npi-pattern-bg {
    background-color: white;
  }

  .npi-pattern-light {
    background-color: #EFE3BA;
  }

  /* Enhanced hover effects */
  .npi-hover-lift {
    @apply transition-all duration-300 hover:-translate-y-1;
  }

  .npi-hover-lift:hover {
    box-shadow:
      0 8px 25px rgba(45, 80, 22, 0.15),
      0 4px 10px rgba(45, 80, 22, 0.1);
  }

  .npi-hover-scale {
    @apply transition-transform duration-200 hover:scale-105;
  }

  .npi-hover-glow {
    @apply transition-all duration-300;
  }

  .npi-hover-glow:hover {
    box-shadow:
      0 0 20px rgba(45, 80, 22, 0.2),
      0 4px 16px rgba(45, 80, 22, 0.1);
  }

  /* Professional borders */
  .npi-border-subtle {
    border: 1px solid rgba(45, 80, 22, 0.1);
  }

  .npi-border-soft {
    border: 1px solid rgba(45, 80, 22, 0.15);
  }

  .npi-border-accent {
    border: 2px solid rgba(45, 80, 22, 0.2);
  }

  /* Professional shadows */
  .npi-shadow-soft {
    box-shadow:
      0 1px 3px rgba(45, 80, 22, 0.08),
      0 1px 2px rgba(45, 80, 22, 0.06);
  }

  .npi-shadow-medium {
    box-shadow:
      0 4px 16px rgba(45, 80, 22, 0.12),
      0 2px 6px rgba(45, 80, 22, 0.08);
  }

  .npi-shadow-strong {
    box-shadow:
      0 8px 32px rgba(45, 80, 22, 0.16),
      0 4px 12px rgba(45, 80, 22, 0.12);
  }

  .npi-shadow-elegant {
    box-shadow:
      0 2px 8px rgba(62, 39, 35, 0.06),
      0 1px 4px rgba(62, 39, 35, 0.04);
  }

  .npi-shadow-crisp {
    box-shadow:
      0 1px 2px rgba(0, 0, 0, 0.05),
      0 0 0 1px rgba(45, 80, 22, 0.05);
  }

  /* Backdrop blur effects */
  .npi-backdrop {
    @apply backdrop-blur-sm bg-background/80;
  }

  .npi-backdrop-strong {
    @apply backdrop-blur-md bg-background/90;
  }

  /* Responsive utilities */
  .npi-responsive-grid {
    @apply grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6;
  }

  .npi-responsive-text {
    @apply text-sm sm:text-base lg:text-lg;
  }

  .npi-responsive-heading {
    @apply text-2xl sm:text-3xl lg:text-4xl xl:text-5xl;
  }

  .npi-responsive-padding {
    @apply px-4 sm:px-6 lg:px-8;
  }

  .npi-responsive-margin {
    @apply mx-4 sm:mx-6 lg:mx-8;
  }

  /* Mobile-first responsive containers */
  .npi-mobile-stack {
    @apply flex flex-col space-y-4 md:flex-row md:space-y-0 md:space-x-6;
  }

  .npi-mobile-center {
    @apply text-center md:text-left;
  }

  /* Premium UI Effects */
  .npi-glass {
    @apply bg-white/10 backdrop-blur-md border border-white/20;
  }

  .npi-glass-dark {
    @apply bg-black/10 backdrop-blur-md border border-black/20;
  }

  .npi-glow {
    box-shadow: 0 0 20px rgba(45, 80, 22, 0.3);
  }

  .npi-glow-brown {
    box-shadow: 0 0 20px rgba(62, 39, 35, 0.3);
  }

  .npi-text-glow {
    text-shadow: 0 0 10px rgba(255, 255, 255, 0.5);
  }

  /* Micro-interactions */
  .npi-pulse-on-hover:hover {
    animation: pulse 1s infinite;
  }

  .npi-bounce-on-hover:hover {
    animation: bounce 0.5s ease-in-out;
  }

  .npi-wiggle-on-hover:hover {
    animation: wiggle 0.5s ease-in-out;
  }

  .npi-float {
    animation: float 3s ease-in-out infinite;
  }

  .npi-rotate-on-hover:hover {
    transform: rotate(5deg);
    transition: transform 0.3s ease;
  }

  /* Interactive elements */
  .npi-interactive {
    @apply transition-all duration-300 cursor-pointer;
  }

  .npi-interactive:hover {
    @apply transform -translate-y-1 shadow-lg;
  }

  .npi-interactive:active {
    @apply transform translate-y-0 shadow-md;
  }

  /* Gradient borders */
  .npi-gradient-border {
    position: relative;
    background: linear-gradient(white, white) padding-box,
                linear-gradient(135deg, hsl(var(--primary)), hsl(var(--secondary))) border-box;
    border: 2px solid transparent;
  }

  /* Shimmer effect */
  .npi-shimmer {
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
    background-size: 200% 100%;
    animation: shimmer 2s infinite;
  }

  /* Parallax sections */
  .npi-parallax {
    transform-style: preserve-3d;
  }

  /* Smooth reveal animations */
  .npi-reveal {
    opacity: 0;
    transform: translateY(50px);
    transition: all 0.8s ease-out;
  }

  .npi-reveal.revealed {
    opacity: 1;
    transform: translateY(0);
  }

  /* Premium button effects */
  .npi-button-premium {
    @apply relative overflow-hidden;
  }

  .npi-button-premium::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s;
  }

  .npi-button-premium:hover::before {
    left: 100%;
  }

  /* Animation classes */
  .animate-fade-in-up {
    animation: fadeInUp 0.8s ease-out forwards;
    opacity: 0;
    transform: translateY(30px);
  }

  .animation-delay-100 {
    animation-delay: 0.1s;
  }

  .animation-delay-200 {
    animation-delay: 0.2s;
  }

  .animation-delay-400 {
    animation-delay: 0.4s;
  }

  .animation-delay-600 {
    animation-delay: 0.6s;
  }

  .animation-delay-800 {
    animation-delay: 0.8s;
  }

  .animation-delay-1000 {
    animation-delay: 1s;
  }

  /* Bento Grid Specific Animations */
  .bento-card-hover {
    @apply transition-all duration-300 hover:scale-[1.02] hover:-translate-y-1;
  }

  .bento-card-featured {
    background: linear-gradient(135deg, hsl(var(--npi-burgundy-500)), hsl(var(--npi-burgundy-700)));
    box-shadow: 0 8px 32px rgba(139, 38, 53, 0.3);
  }

  .bento-card-accent {
    background: linear-gradient(135deg, hsl(var(--npi-green-500)), hsl(var(--npi-green-700)));
    box-shadow: 0 8px 32px rgba(45, 80, 22, 0.3);
  }

  .bento-grid-stagger {
    animation: staggerIn 0.6s ease-out forwards;
  }

  .bento-parallax-bg {
    background-attachment: fixed;
    background-position: center;
    background-repeat: no-repeat;
    background-size: cover;
  }

  /* Professional Glass Effects - Updated with New Palette */
  .npi-glass-card {
    background: hsl(45 25% 90% / 0.8);     /* #E5E1DC - Light cream */
    backdrop-filter: blur(20px);
    border: 1px solid hsl(45 20% 82% / 0.3); /* #CEC9BC - Medium cream */
    box-shadow: 0 8px 32px hsl(60 8% 55% / 0.1); /* #8D8F78 - Sage */
  }

  .npi-glass-rust {
    background: hsl(25 60% 26% / 0.1);     /* #6E3C19 - Medium rust */
    backdrop-filter: blur(20px);
    border: 1px solid hsl(25 60% 26% / 0.2);
    box-shadow: 0 8px 32px hsl(25 60% 26% / 0.2);
  }

  .npi-glass-forest {
    background: hsl(150 45% 12% / 0.1);    /* #102820 - Deep forest */
    backdrop-filter: blur(20px);
    border: 1px solid hsl(150 45% 12% / 0.2);
    box-shadow: 0 8px 32px hsl(150 45% 12% / 0.2);
  }

  .npi-glass-tan {
    background: hsl(25 30% 52% / 0.1);     /* #A7795E - Warm tan */
    backdrop-filter: blur(20px);
    border: 1px solid hsl(25 30% 52% / 0.2);
    box-shadow: 0 8px 32px hsl(25 30% 52% / 0.2);
  }

  /* Interactive Color Morphing Effects */
  .npi-morph-colors {
    transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1);
    background: linear-gradient(135deg,
      hsl(var(--npi-cream-100)) 0%,
      hsl(var(--background)) 100%);
  }

  .npi-morph-colors:hover {
    background: linear-gradient(135deg,
      hsl(var(--npi-rust-500) / 0.15) 0%,
      hsl(var(--npi-tan) / 0.1) 25%,
      hsl(var(--npi-sage-500) / 0.1) 50%,
      hsl(var(--npi-forest-500) / 0.15) 100%);
    transform: scale(1.02) translateY(-3px);
    box-shadow:
      0 12px 40px hsl(var(--npi-rust-500) / 0.2),
      0 4px 12px hsl(var(--npi-sage-500) / 0.1);
  }

  /* Simple Border Effects */
  .npi-border-lively {
    border: 2px solid #8A3E25;
    background: white;
    transition: all 0.4s ease;
  }

  .npi-border-lively:hover {
    border: 2px solid #725242;
    background: #EFE3BA;
  }

  /* Enhanced Color Contrast Utilities */
  .npi-text-contrast {
    color: #000000;  /* Black */
  }

  .npi-text-warm {
    color: #725242;  /* Brown */
  }

  .npi-text-accent {
    color: #8A3E25;  /* Reddish brown */
  }

  .npi-bg-warm {
    background-color: #EFE3BA;  /* Cream */
  }

  .npi-bg-medium {
    background-color: #CEC9BC;  /* Medium cream */
  }

  .npi-bg-accent {
    background-color: #A7795E;  /* Warm tan */
  }

  .npi-bg-primary {
    background-color: #6E3C19;  /* Medium rust */
  }

  .npi-bg-secondary {
    background-color: #102820;  /* Deep forest */
  }

  .npi-bg-sage {
    background-color: #8D8F78;  /* Sage */
  }

  .npi-bg-olive {
    background-color: #4C6444;  /* Olive */
  }

  .npi-bg-beige {
    background-color: #CABA9C;  /* Beige */
  }

  .npi-bg-brown {
    background-color: #8A6240;  /* Brown medium */
  }

  .npi-bg-dark-brown {
    background-color: #46372A;  /* Dark brown */
  }

  .npi-bg-charcoal {
    background-color: #2F2C29;  /* Warm charcoal */
  }

  /* Unique Section Design Elements */
  .npi-floating-elements::before {
    content: '';
    position: absolute;
    top: 10%;
    left: 5%;
    width: 100px;
    height: 100px;
    background: linear-gradient(45deg, #A7795E, #8A6240);
    border-radius: 50%;
    opacity: 0.1;
    animation: float 6s ease-in-out infinite;
  }

  .npi-floating-elements::after {
    content: '';
    position: absolute;
    bottom: 15%;
    right: 8%;
    width: 80px;
    height: 80px;
    background: linear-gradient(45deg, #4C6444, #102820);
    border-radius: 50%;
    opacity: 0.08;
    animation: float 8s ease-in-out infinite reverse;
  }

  .npi-geometric-pattern {
    background-image:
      linear-gradient(30deg, #8D8F78 12%, transparent 12.5%, transparent 87%, #8D8F78 87.5%, #8D8F78),
      linear-gradient(150deg, #8D8F78 12%, transparent 12.5%, transparent 87%, #8D8F78 87.5%, #8D8F78),
      linear-gradient(30deg, #8D8F78 12%, transparent 12.5%, transparent 87%, #8D8F78 87.5%, #8D8F78),
      linear-gradient(150deg, #8D8F78 12%, transparent 12.5%, transparent 87%, #8D8F78 87.5%, #8D8F78);
    background-size: 80px 140px;
    background-position: 0 0, 0 0, 40px 70px, 40px 70px;
    opacity: 0.03;
  }

  .npi-section-divider {
    position: relative;
  }

  .npi-section-divider::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(90deg,
      transparent 0%,
      #A7795E 25%,
      #6E3C19 50%,
      #4C6444 75%,
      transparent 100%);
  }

  @keyframes float {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    50% { transform: translateY(-20px) rotate(180deg); }
  }

  @keyframes shimmer {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
  }

  .npi-shimmer {
    position: relative;
    overflow: hidden;
  }

  .npi-shimmer::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg,
      transparent 0%,
      rgba(167, 121, 94, 0.1) 50%,
      transparent 100%);
    animation: shimmer 3s infinite;
  }

  /* Professional Micro-interactions */
  .npi-magnetic {
    transition: transform 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  }

  .npi-magnetic:hover {
    transform: scale(1.05) translateY(-2px);
  }

  .npi-tilt {
    transition: transform 0.3s ease;
  }

  .npi-tilt:hover {
    transform: perspective(1000px) rotateX(5deg) rotateY(5deg);
  }

  .npi-depth {
    box-shadow:
      0 1px 3px rgba(0, 0, 0, 0.12),
      0 1px 2px rgba(0, 0, 0, 0.24);
    transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
  }

  .npi-depth:hover {
    box-shadow:
      0 14px 28px rgba(0, 0, 0, 0.25),
      0 10px 10px rgba(0, 0, 0, 0.22);
  }

  /* Gradient Text Effects */
  .npi-gradient-text-burgundy {
    background: linear-gradient(135deg, hsl(var(--npi-burgundy-500)), hsl(var(--npi-burgundy-700)));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }

  /* Background Patterns and Designs */
  .npi-pattern-dots {
    background-image: radial-gradient(circle, rgba(45, 80, 22, 0.1) 1px, transparent 1px);
    background-size: 20px 20px;
  }

  .npi-pattern-grid {
    background-image:
      linear-gradient(rgba(45, 80, 22, 0.05) 1px, transparent 1px),
      linear-gradient(90deg, rgba(45, 80, 22, 0.05) 1px, transparent 1px);
    background-size: 30px 30px;
  }

  .npi-pattern-diagonal {
    background-image: repeating-linear-gradient(
      45deg,
      transparent,
      transparent 10px,
      rgba(45, 80, 22, 0.03) 10px,
      rgba(45, 80, 22, 0.03) 20px
    );
  }

  .npi-pattern-organic {
    background-image:
      radial-gradient(circle at 25% 25%, rgba(248, 246, 240, 0.8) 0%, transparent 50%),
      radial-gradient(circle at 75% 75%, rgba(45, 80, 22, 0.05) 0%, transparent 50%),
      radial-gradient(circle at 50% 50%, rgba(62, 39, 35, 0.03) 0%, transparent 50%);
    background-size: 100px 100px, 150px 150px, 80px 80px;
  }

  .npi-gradient-subtle {
    background: linear-gradient(135deg,
      hsl(var(--npi-cream-200)) 0%,
      hsl(var(--background)) 50%,
      hsl(var(--npi-cream-200)) 100%);
  }

  .npi-gradient-warm {
    background: linear-gradient(135deg,
      hsl(var(--npi-rust-500) / 0.05) 0%,
      hsl(var(--npi-cream-100)) 50%,
      hsl(var(--npi-forest-500) / 0.05) 100%);
  }

  /* Enhanced Vibrant Color Blending Effects */
  .npi-gradient-earth {
    background: linear-gradient(135deg,
      #6E3C19 0%,      /* Medium rust */
      #8A6240 35%,     /* Brown medium */
      #A7795E 70%,     /* Warm tan */
      #CABA9C 100%);   /* Beige */
  }

  .npi-gradient-forest {
    background: linear-gradient(135deg,
      #102820 0%,      /* Deep forest */
      #4C6444 40%,     /* Olive */
      #8D8F78 80%,     /* Sage */
      #CEC9BC 100%);   /* Medium cream */
  }

  .npi-gradient-cream {
    background: linear-gradient(135deg,
      #E5E1DC 0%,      /* Light cream */
      #CABA9C 33%,     /* Beige */
      #CEC9BC 66%,     /* Medium cream */
      #8D8F78 100%);   /* Sage */
  }

  .npi-gradient-vibrant {
    background: linear-gradient(135deg,
      #34170D 0%,      /* Dark rust */
      #6E3C19 25%,     /* Medium rust */
      #A7795E 50%,     /* Warm tan */
      #4C6444 75%,     /* Olive */
      #102820 100%);   /* Deep forest */
  }

  .npi-gradient-warm {
    background: linear-gradient(135deg,
      #46372A 0%,      /* Dark brown */
      #8A6240 30%,     /* Brown medium */
      #A7795E 60%,     /* Warm tan */
      #CABA9C 100%);   /* Beige */
  }

  /* Bright Popping Color Gradients */
  .npi-gradient-pop-forest {
    background: linear-gradient(135deg,
      #102820 0%,      /* Deep forest */
      #4C6444 25%,     /* Olive */
      #8D8F78 50%,     /* Sage */
      #CABA9C 75%,     /* Beige */
      #E5E1DC 100%);   /* Light cream */
  }

  .npi-gradient-pop-rust {
    background: linear-gradient(135deg,
      #34170D 0%,      /* Dark rust */
      #6E3C19 25%,     /* Medium rust */
      #8A6240 50%,     /* Brown medium */
      #A7795E 75%,     /* Warm tan */
      #CABA9C 100%);   /* Beige */
  }

  .npi-gradient-pop-sage {
    background: linear-gradient(135deg,
      #8D8F78 0%,      /* Sage */
      #CEC9BC 25%,     /* Medium cream */
      #CABA9C 50%,     /* Beige */
      #A7795E 75%,     /* Warm tan */
      #E5E1DC 100%);   /* Light cream */
  }

  .npi-gradient-rainbow {
    background: linear-gradient(135deg,
      #102820 0%,      /* Deep forest */
      #4C6444 14%,     /* Olive */
      #8D8F78 28%,     /* Sage */
      #A7795E 42%,     /* Warm tan */
      #8A6240 56%,     /* Brown medium */
      #6E3C19 70%,     /* Medium rust */
      #46372A 84%,     /* Dark brown */
      #34170D 100%);   /* Dark rust */
  }

  /* Bright Text Colors */
  .npi-text-pop-forest {
    color: #102820;
  }

  .npi-text-pop-rust {
    color: #6E3C19;
  }

  .npi-text-pop-sage {
    color: #8D8F78;
  }

  .npi-text-pop-tan {
    color: #A7795E;
  }

  .npi-gradient-interactive {
    background: linear-gradient(135deg,
      hsl(25 60% 26% / 0.1) 0%,    /* #6E3C19 - Medium rust */
      hsl(25 30% 52% / 0.1) 20%,   /* #A7795E - Warm tan */
      hsl(60 8% 55% / 0.1) 40%,    /* #8D8F78 - Sage */
      hsl(150 45% 12% / 0.1) 60%,  /* #102820 - Deep forest */
      hsl(80 20% 35% / 0.1) 80%,   /* #4C6444 - Olive */
      hsl(45 20% 82% / 0.1) 100%); /* #CEC9BC - Medium cream */
    background-size: 200% 200%;
    animation: gradientShift 8s ease infinite;
  }

  .npi-hover-gradient {
    transition: all 0.4s ease;
    background: linear-gradient(135deg,
      hsl(45 25% 90%) 0%,      /* #E5E1DC - Light cream */
      hsl(45 25% 90%) 100%);   /* #E5E1DC - Light cream */
  }

  .npi-hover-gradient:hover {
    background: linear-gradient(135deg,
      hsl(25 60% 26% / 0.1) 0%,    /* #6E3C19 - Medium rust */
      hsl(25 30% 52% / 0.1) 50%,   /* #A7795E - Warm tan */
      hsl(60 8% 55% / 0.1) 100%);  /* #8D8F78 - Sage */
    transform: translateY(-2px);
    box-shadow: 0 8px 25px hsl(25 60% 26% / 0.15);
  }

  .npi-gradient-text-reddish-brown {
    background: linear-gradient(135deg, hsl(var(--npi-reddish-brown-500)), hsl(var(--npi-reddish-brown-600)));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }

  .npi-gradient-text-gold {
    background: linear-gradient(135deg, hsl(var(--npi-gold-500)), hsl(var(--npi-gold-700)));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }

  .npi-gradient-text-green {
    background: linear-gradient(135deg, hsl(var(--npi-green-500)), hsl(var(--npi-green-700)));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }

  /* Advanced Border Effects */
  .npi-border-gradient {
    position: relative;
    background: linear-gradient(white, white) padding-box,
                linear-gradient(135deg, hsl(var(--npi-burgundy-500)), hsl(var(--npi-gold-500)), hsl(var(--npi-green-500))) border-box;
    border: 2px solid transparent;
  }

  .npi-border-animated {
    position: relative;
    overflow: hidden;
  }

  .npi-border-animated::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
    transition: left 0.5s;
  }

  .npi-border-animated:hover::before {
    left: 100%;
  }

  /* Scroll-triggered animations */
  .npi-scroll-reveal {
    opacity: 0;
    transform: translateY(50px);
    transition: all 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  }

  .npi-scroll-reveal.revealed {
    opacity: 1;
    transform: translateY(0);
  }

  .npi-scroll-scale {
    opacity: 0;
    transform: scale(0.8);
    transition: all 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  }

  .npi-scroll-scale.revealed {
    opacity: 1;
    transform: scale(1);
  }

  /* Smooth scrolling */
  html {
    scroll-behavior: smooth;
  }

  @media (prefers-reduced-motion: reduce) {
    html {
      scroll-behavior: auto;
    }
  }
}

/* Keyframe animations */
@keyframes fadeInUp {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-50px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(50px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes wiggle {
  0%, 100% { transform: rotate(0deg); }
  25% { transform: rotate(-3deg); }
  75% { transform: rotate(3deg); }
}

@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
}

@keyframes shimmer {
  0% { background-position: -200% 0; }
  100% { background-position: 200% 0; }
}

@keyframes pulse-glow {
  0%, 100% { box-shadow: 0 0 5px rgba(45, 80, 22, 0.3); }
  50% { box-shadow: 0 0 20px rgba(45, 80, 22, 0.6); }
}

@keyframes staggerIn {
  from {
    opacity: 0;
    transform: translateY(20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

@keyframes slideInFromLeft {
  from {
    opacity: 0;
    transform: translateX(-100px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInFromRight {
  from {
    opacity: 0;
    transform: translateX(100px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes morphBackground {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.02);
  }
}

@keyframes gradientShift {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

/* Responsive animations - reduce motion for users who prefer it */
@media (prefers-reduced-motion: reduce) {
  .animate-fade-in-up,
  .npi-hover-lift,
  .npi-hover-scale,
  .npi-float,
  .npi-shimmer {
    animation: none;
    transition: none;
  }
}
