'use client'

import React from 'react'
import { motion } from 'framer-motion'
import {
  NPISection,
  NPISectionHeader,
  NPISectionTitle,
  NPISectionDescription,
} from '@/components/ui/npi-section'
import {
  NPICard,
  NPICardHeader,
  NPICardTitle,
  NPICardContent,
  NPICardFooter,
} from '@/components/ui/npi-card'
import { NPIButton } from '@/components/ui/npi-button'
import Image from 'next/image'
import Link from 'next/link'
import { Quote, MapPin, TrendingUp } from 'lucide-react'

interface SuccessStory {
  title: string
  summary: string
  image: string
  location: string
  category: string
  impact: string
  testimonial?: {
    quote: string
    author: string
    role: string
  }
  link: string
}

interface NPISuccessStoriesProps {
  title?: string
  description?: string
  stories?: SuccessStory[]
}

export const NPISuccessStoriesBlock: React.FC<NPISuccessStoriesProps> = ({
  title = 'Success Stories',
  description = 'Real impact, real change. Discover how our initiatives are transforming lives and communities across Kenya.',
  stories = [
    {
      title: 'Aloe Vera Cooperative Transforms Rural Economy',
      summary:
        "A women's cooperative in Baringo County successfully commercialized traditional aloe vera products, increasing household incomes by 300% and creating 150 jobs.",
      image: '/assets/product 1.jpg',
      location: 'Baringo County',
      category: 'Community-Led Innovation',
      impact: '150 jobs created, 300% income increase',
      testimonial: {
        quote:
          'NPI helped us turn our traditional knowledge into a thriving business. Now our children can go to school and our community has hope.',
        author: 'Mary Chepkemoi',
        role: 'Cooperative Chairwoman',
      },
      link: '/success-stories/aloe-cooperative',
    },
    {
      title: 'Youth-Led Moringa Processing Enterprise',
      summary:
        'Young entrepreneurs in Turkana developed a sustainable moringa processing facility, creating nutritious products while preserving traditional knowledge.',
      image: '/assets/product 2.jpg',
      location: 'Turkana County',
      category: 'Youth Empowerment',
      impact: '50 youth employed, 200 farmers supported',
      testimonial: {
        quote:
          "We learned to combine our ancestors' wisdom with modern technology. Now we're exporting moringa products across East Africa.",
        author: 'John Ekale',
        role: 'Youth Group Leader',
      },
      link: '/success-stories/moringa-youth',
    },
    {
      title: 'Traditional Medicine IP Protection Success',
      summary:
        'Maasai community successfully registered traditional medicine formulations, securing intellectual property rights and establishing sustainable revenue streams.',
      image: '/assets/product 3.jpg',
      location: 'Kajiado County',
      category: 'IP Registration',
      impact: '5 patents registered, community rights protected',
      testimonial: {
        quote:
          'Our traditional knowledge is now protected by law. We can share it with the world while ensuring our community benefits.',
        author: 'Elder Joseph Sankale',
        role: 'Traditional Healer',
      },
      link: '/success-stories/maasai-ip',
    },
  ],
}) => {
  return (
    <NPISection size="sm" className="bg-[#EFE3BA] relative overflow-hidden">
      {/* Simple Background Elements */}
      <div className="absolute inset-0 pointer-events-none">
        {/* Subtle pattern */}
        <div
          className="absolute inset-0 opacity-[0.02]"
          style={{
            backgroundImage: `
            linear-gradient(90deg, #725242 1px, transparent 1px),
            linear-gradient(#725242 1px, transparent 1px)
          `,
            backgroundSize: '60px 60px',
          }}
        />
      </div>

      <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <NPISectionHeader className="text-center mb-6">
          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            whileInView={{ opacity: 1, scale: 1 }}
            viewport={{ once: true }}
            transition={{ duration: 0.8 }}
            className="inline-flex items-center px-6 py-3 bg-[#8A3E25]/15 border border-[#8A3E25]/30 text-[#8A3E25] text-sm font-semibold mb-4"
          >
            <TrendingUp className="w-4 h-4 mr-3 text-[#25718A]" />
            Success Stories
          </motion.div>
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.8, delay: 0.2 }}
          >
            <NPISectionTitle className="leading-[1.1] tracking-[-0.02em] mb-3 text-black font-bold text-4xl">
              {title}
            </NPISectionTitle>
          </motion.div>
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.8, delay: 0.4 }}
          >
            <NPISectionDescription className="font-light leading-[1.7] text-lg max-w-3xl mx-auto">
              {description}
            </NPISectionDescription>
          </motion.div>
        </NPISectionHeader>

        <div className="grid lg:grid-cols-3 gap-4 max-w-7xl mx-auto">
          {stories.map((story, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 40, scale: 0.9 }}
              whileInView={{ opacity: 1, y: 0, scale: 1 }}
              viewport={{ once: true }}
              transition={{
                duration: 0.8,
                delay: index * 0.2,
                type: 'spring',
                stiffness: 80,
              }}
              whileHover={{ y: -12 }}
            >
              <NPICard
                className={`overflow-hidden backdrop-blur-sm shadow-xl border-4 npi-hover-lift transition-all duration-500 hover:shadow-2xl group h-full hover:scale-[1.05] hover:-translate-y-2 ${
                  index % 3 === 0
                    ? 'bg-gradient-to-br from-[#4C6444] to-[#102820] border-[#8D8F78] hover:border-[#4C6444] hover:shadow-[#4C6444]/50'
                    : index % 3 === 1
                      ? 'bg-gradient-to-br from-[#8A6240] to-[#6E3C19] border-[#A7795E] hover:border-[#8A6240] hover:shadow-[#8A6240]/50'
                      : 'bg-gradient-to-br from-[#A7795E] to-[#CABA9C] border-[#8D8F78] hover:border-[#A7795E] hover:shadow-[#A7795E]/50'
                }`}
              >
                <div className="relative h-64 w-full">
                  <Image
                    src={story.image}
                    alt={story.title}
                    fill
                    className="object-cover group-hover:scale-110 transition-transform duration-700"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-black/20 to-transparent" />
                  <div className="absolute top-4 right-4">
                    <motion.div
                      initial={{ scale: 0 }}
                      whileInView={{ scale: 1 }}
                      transition={{ delay: 0.5, type: 'spring' }}
                      className={`backdrop-blur-sm px-4 py-2 text-sm font-bold text-[#E5E1DC] shadow-lg ${
                        index % 3 === 0
                          ? 'bg-gradient-to-r from-[#8D8F78] to-[#CEC9BC]'
                          : index % 3 === 1
                            ? 'bg-gradient-to-r from-[#CABA9C] to-[#E5E1DC]'
                            : 'bg-gradient-to-r from-[#E5E1DC] to-[#CEC9BC]'
                      }`}
                    >
                      {story.category}
                    </motion.div>
                  </div>
                  <div className="absolute bottom-4 left-4 right-4">
                    <div className="flex items-center text-white/90 text-sm font-light mb-2">
                      <MapPin className="w-4 h-4 mr-2" />
                      {story.location}
                    </div>
                    <div className="flex items-center text-npi-gold text-sm font-semibold">
                      <TrendingUp className="w-4 h-4 mr-2" />
                      {story.impact}
                    </div>
                  </div>
                </div>

                <NPICardHeader className="p-6">
                  <NPICardTitle className="text-xl font-bold text-foreground leading-tight mb-3 group-hover:text-primary transition-colors">
                    {story.title}
                  </NPICardTitle>
                </NPICardHeader>

                <NPICardContent className="px-6 pb-4">
                  <p className="text-muted-foreground leading-[1.7] mb-6 font-light text-sm">
                    {story.summary}
                  </p>

                  {story.testimonial && (
                    <motion.blockquote
                      initial={{ opacity: 0, x: -20 }}
                      whileInView={{ opacity: 1, x: 0 }}
                      transition={{ delay: 0.3 }}
                      className="border-l-4 border-npi-gold pl-4 bg-tertiary/30 p-4 relative"
                    >
                      <Quote className="absolute top-2 right-2 w-5 h-5 text-npi-gold/50" />
                      <p className="mb-3 text-foreground italic font-light leading-[1.6] text-sm">
                        &ldquo;{story.testimonial.quote}&rdquo;
                      </p>
                      <footer className="text-muted-foreground text-xs">
                        <strong className="font-semibold text-primary">
                          {story.testimonial.author}
                        </strong>
                        , {story.testimonial.role}
                      </footer>
                    </motion.blockquote>
                  )}
                </NPICardContent>

                <NPICardFooter className="p-6 pt-0">
                  <NPIButton
                    asChild
                    variant="outline"
                    className="w-full border-2 border-primary text-primary hover:bg-primary hover:text-primary-foreground font-medium transition-all duration-300 npi-hover-lift"
                  >
                    <Link href={story.link}>Read Full Story</Link>
                  </NPIButton>
                </NPICardFooter>
              </NPICard>
            </motion.div>
          ))}
        </div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.8, delay: 0.6 }}
          className="text-center mt-8"
        >
          <NPIButton
            asChild
            size="lg"
            variant="primary"
            className="bg-gradient-to-r from-[#34170D] to-[#6E3C19] hover:from-[#6E3C19] hover:to-[#8A6240] text-[#E5E1DC] font-bold px-12 py-5 npi-hover-lift shadow-xl hover:shadow-2xl hover:shadow-[#6E3C19]/50 transition-all duration-300 border-2 border-[#8A6240] hover:border-[#A7795E]"
          >
            <Link href="/success-stories">View All Success Stories</Link>
          </NPIButton>
        </motion.div>
      </div>
    </NPISection>
  )
}
