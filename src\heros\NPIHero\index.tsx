'use client'

import React from 'react'
import Link from 'next/link'
import { NPIParallaxHero } from '@/components/ui/npi-parallax-hero'
import { NPIButton } from '@/components/ui/npi-button'
import { motion } from 'framer-motion'

export const NPIHeroComponent: React.FC = () => {
  return (
    <NPIParallaxHero
      backgroundVideo="/assets/hero.mp4"
      height="large"
      parallaxSpeed={0.3}
      overlayOpacity={0.7}
      className="relative min-h-[85vh] max-h-[95vh] -mt-16 pt-16"
    >
      {/* Custom Layout Container - Absolute positioning for extreme corners */}
      <div className="absolute inset-0 text-white z-20">
        {/* Top Left Section - Aligned with navbar container */}
        <motion.div
          className="absolute top-0 left-0 pt-26 px-4 sm:px-6 lg:px-25 max-w-7xl"
          initial={{ opacity: 0, x: -50 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.8, delay: 0.2 }}
        >
          <div className="max-w-lg space-y-6">
            {/* Organization info */}
            <motion.p
              className="text-sm md:text-base text-white/90 font-light"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.6 }}
            >
              Natural Products Industry Initiative
            </motion.p>
            <motion.p
              className="text-base md:text-lg lg:text-xl text-white/95 font-light leading-relaxed"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.8 }}
            >
              Transforming Kenya&apos;s rich natural heritage into sustainable economic
              opportunities.
            </motion.p>

            {/* CTA Buttons moved to top left */}
            <motion.div
              className="flex flex-col sm:flex-row gap-4"
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.4 }}
            >
              <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
                <NPIButton
                  asChild
                  size="lg"
                  variant="primary"
                  className="bg-[#8A3E25] hover:bg-[#725242] text-white font-bold px-8 py-4 shadow-xl hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-1 border-2 border-[#8A3E25] hover:border-[#725242]"
                >
                  <Link href="/about" className="flex items-center gap-2">
                    Learn More
                    <motion.span
                      animate={{ x: [0, 3, 0] }}
                      transition={{ duration: 1.5, repeat: Infinity, ease: 'easeInOut' }}
                    >
                      →
                    </motion.span>
                  </Link>
                </NPIButton>
              </motion.div>

              <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
                <NPIButton
                  asChild
                  size="lg"
                  variant="outline"
                  className="border-2 border-[#EFE3BA] bg-[#EFE3BA]/30 text-white hover:bg-[#EFE3BA] hover:text-black backdrop-blur-md px-8 py-4 transition-all duration-300 shadow-lg hover:shadow-xl font-bold hover:border-[#EFE3BA]"
                >
                  <Link href="/partnerships" className="flex items-center gap-2">
                    Partner With Us
                    <motion.span
                      animate={{ rotate: [0, 10, 0] }}
                      transition={{ duration: 2, repeat: Infinity, ease: 'easeInOut' }}
                    >
                      🤝
                    </motion.span>
                  </Link>
                </NPIButton>
              </motion.div>
            </motion.div>
          </div>
        </motion.div>

        {/* Bottom Right Section - Aligned with navbar container */}
        <motion.div
          className="absolute bottom-0 right-0 pb-16 px-4 sm:px-6 lg:px-8 max-w-7xl ml-auto"
          initial={{ opacity: 0, x: 50 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.8, delay: 0.4 }}
        >
          <div className="max-w-xl text-right">
            <motion.h1
              className="text-2xl md:text-3xl lg:text-4xl xl:text-5xl font-bold mb-4 leading-[1.1] tracking-[-0.02em]"
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.6 }}
            >
              Harnessing Indigenous Wealth for{' '}
              <span className="text-transparent bg-clip-text bg-gradient-to-r from-[#102820] via-[#4C6444] via-[#A7795E] via-[#CABA9C] to-[#8A6240]">
                Sustainable Growth
              </span>
            </motion.h1>
          </div>
        </motion.div>

        {/* Arrow Scroll Indicator - True Bottom Center */}
        {/* <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 1.2, duration: 0.8 }}
          className="absolute bottom-1 left-1/2 transform -translate-x-1/2 z-30"
        >
          <motion.div
            className="w-12 h-12 rounded-full bg-gradient-to-br from-[#8D8F78]/40 via-[#CABA9C]/30 to-[#A7795E]/35 backdrop-blur-md border-2 border-[#E5E1DC]/50 flex items-center justify-center cursor-pointer hover:bg-gradient-to-br hover:from-[#102820]/60 hover:via-[#4C6444]/50 hover:to-[#8D8F78]/60 hover:border-[#CABA9C]/80 transition-all duration-300 shadow-lg hover:shadow-xl"
            animate={{ y: [0, 8, 0] }}
            transition={{ duration: 2, repeat: Infinity, ease: 'easeInOut' }}
            onClick={() => window.scrollTo({ top: window.innerHeight, behavior: 'smooth' })}
          >
            <motion.svg
              className="w-6 h-6 text-[#E5E1DC] group-hover:text-[#CABA9C] transition-colors duration-300"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
              animate={{ y: [0, 3, 0] }}
              transition={{ duration: 2, repeat: Infinity, ease: 'easeInOut', delay: 0.2 }}
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M19 14l-7 7m0 0l-7-7m7 7V3"
              />
            </motion.svg>
          </motion.div>
        </motion.div> */}
      </div>
    </NPIParallaxHero>
  )
}
